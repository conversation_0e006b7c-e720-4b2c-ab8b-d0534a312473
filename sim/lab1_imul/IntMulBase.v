//========================================================================
// Integer Multiplier Fixed-Latency Implementation
//========================================================================

`ifndef LAB1_IMUL_INT_MUL_BASE_V
`define LAB1_IMUL_INT_MUL_BASE_V

`include "vc/trace.v"

// ''' LAB TASK ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
// Define datapath and control unit here.
// '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''

// DATAPATH

module lab1_imul_datapath
(
  input logic clk,
  input logic reset,

  // Data signals

  input  logic [63:0] istream_msg,
  output logic [31:0] ostream_msg,

  // Control signals  

  input logic b_mux_sel,
  input logic a_mux_sel,
  input logic result_mux_sel,
  input logic result_en,
  input logic add_mux_sel,

  // Status signals

  output logic b_lsb
);

  localparam c_nbits = 32;

  // Split a and b operands
  logic [c_nbits-1:0] istream_msg_a;
  assign istream_msg_a = istream_msg[63:32];

  logic [c_nbits-1:0] istream_msg_b;
  assign istream_msg_b = istream_msg[31:0];

  // B Mux

  logic [c_nbits-1:0] rShift_out;
  logic [c_nbits-1:0] b_mux_out;

  vc_Mux2#(c_nbits) b_mux 
  (
    .sel (b_mux_sel),
    .in0 (rShift_out),
    .in1 (istream_msg_b),
    .out (b_mux_out)
  );

  // A Mux

  logic [c_nbits-1:0] lShift_out;
  logic [c_nbits-1:0] a_mux_out;

  vc_Mux2#(c_nbits) a_mux 
  (
    .sel (a_mux_sel),
    .in0 (lShift_out),
    .in1 (istream_msg_a),
    .out (a_mux_out)
  );

  // Result Mux

  logic [c_nbits-1:0] add_mux_out;
  logic [c_nbits-1:0] result_mux_out;

  vc_Mux2#(c_nbits) result_mux 
  (
    .sel (result_mux_sel),
    .in0 (add_mux_out),
    .in1 (1'b0),
    .out (result_mux_out)
  );

  // Add Mux

  logic [c_nbits-1:0] add_out;
  logic [c_nbits-1:0] result_reg_out;

  vc_Mux2#(c_nbits) add_mux
  (
    .sel (add_mux_sel),
    .in0 (add_out),
    .in1 (result_reg_out),
    .out (add_mux_out)
  );

  // B register

  logic [c_nbits-1:0] b_reg_out;

  vc_ResetReg#(c_nbits) b_reg
  (
    .clk (clk),
    .reset (reset),
    .d (b_mux_out),
    .q (b_reg_out)
  );

  // A register

  logic [c_nbits-1:0] a_reg_out;

  vc_ResetReg#(c_nbits) a_reg
  (
    .clk (clk),
    .reset (reset),
    .d (a_mux_out),
    .q (a_reg_out)
  );

  // Result register

  vc_ResetReg#(c_nbits) result_reg
  (
    .clk (clk),
    .reset (reset),
    .d (result_mux_out),
    .q (result_reg_out)
  );

  // Adder

  vc_SimpleAdder#(c_nbits) adder
  (
    .in0 (a_reg_out),
    .in1 (result_reg_out),
    .out (add_out)
  );

  // Left shift

  logic [c_nbits-1:0] lShift_out;

  vc_LeftLogicalShifter#(.p_nbits(c_nbits), .p_shamt_nbits(1)) lShifter
  (
    .in (a_reg_out),
    .shamt (1'b1),
    .out (lShift_out)
  );

  // Right shift

  logic [c_nbits-1:0] rShift_out;

  vc_RightLogicalShifter#(.p_nbits(c_nbits), .p_shamt_nbits(1)) rShifter
  (
    .in (b_reg_out),
    .shamt (1'b1),
    .out (rShift_out)
  );

  // Extract LSB of B register for control logic
  assign b_lsb = b_reg_out[0];

  // Connect output
  assign ostream_msg = result_reg_out;

endmodule


// CONTROL UNIT

module lab1_imul_control
(
  input logic clk,
  input logic reset,

  // Stream interface

  input  logic        istream_val,
  output logic        istream_rdy,
  input  logic        ostream_rdy,
  output logic        ostream_val,

  // Control signals (outputs to datapath)

  output logic b_mux_sel,
  output logic a_mux_sel,
  output logic result_mux_sel,
  output logic result_en,
  output logic add_mux_sel,

  // Status signals (inputs from datapath)

  input logic b_lsb
);

  function void cs
  (
    input logic cs_istream_rdy,
    input logic cs_ostream_val,
    input logic b_mux_sel,
    input logic a_mux_sel,
    input logic result_mux_sel,
    input logic result_en,
    input logic add_mux_sel
  );
  begin
    istream_rdy = cs_istream_rdy;
    ostream_val = cs_ostream_val;
    b_mux_sel = cs_b_mux_sel;
    a_mux_sel = cs_a_mux_sel;
    result_mux_sel = cs_result_mux_sel;
    result_en = cs_result_en;
    add_mux_sel = cs_add_mux_sel;
  end
  endfunction

  logic do_add_shift;
  logic do_shift;
  logic count_done;

  // Counter

  vc_BasicCounter#(.p_count_nbits(6), .p_count_max_value(32)) counter
  (
    .clk (clk),
    .reset (reset),
    .clear (1'b0),
    .increment(1'b1),
    .decrement(1'b0),
    .count_is_max(count_done)
  );

  assign do_add_shift = !count_done && b_lsb == 1;
  assign do_shift = !count_done && b_lsb == 0;

  logic [2:0] current_state;
  localparam state_idle = 2'b00;
  localparam state_calc = 2'b01;
  localparam state_done = 2'b10;
endmodule

// Sequential block for state element

always @(posedge clk) begin
  if (reset) current_state <= state_idle;
  else current_state <= next_state;
end

// Combinational block for state transitions

always_comb begin
  case (current_state)
    state_idle: begin
      if (istream_val) next_state = state_calc;
      else next_state = state_idle;
    end

    state_calc: begin
      if (count_done) next_state = state_done;
      else next_state = state_calc;
    end

    state_done: begin
      if (ostream_rdy) next_state = state_idle;
      else next_state = state_done;
    end

    default: next_state = state_idle;
  endcase
end

// Combinational block for state outputs

always_comb begin
  case (current_state)
    state_idle: cs(1, 0, 0, 0, 1, 0, 0);
    state_calc: if (do_add_shift) cs(0, 0, 0, 0, 0, 1, 0);
    else if (do_shift) cs(0, 0, 0, 0, 0, 1, 1);
    state_done: cs(0, 1, 'x, 'x, 0, 1, 1);
    default: cs(x, x, x, x, x, x, x);
  endcase
end

//========================================================================
// Integer Multiplier Fixed-Latency Implementation
//========================================================================

module lab1_imul_IntMulBase
(
  input  logic        clk,
  input  logic        reset,

  input  logic        istream_val,
  output logic        istream_rdy,
  input  logic [63:0] istream_msg,

  output logic        ostream_val,
  input  logic        ostream_rdy,
  output logic [31:0] ostream_msg
);

  // ''' LAB TASK ''''''''''''''''''''''''''''''''''''''''''''''''''''''''
  // Instantiate datapath and control models here and then connect them
  // together.
  // '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''

  lab1_imul_datapath dpath
  (
    .*
  );

  lab1_imul_cony ctrl
  (
    .*
  );


  //----------------------------------------------------------------------
  // Line Tracing
  //----------------------------------------------------------------------

  `ifndef SYNTHESIS

  logic [`VC_TRACE_NBITS-1:0] str;
  `VC_TRACE_BEGIN
  begin

    $sformat( str, "%x", istream_msg );
    vc_trace.append_val_rdy_str( trace_str, istream_val, istream_rdy, str );

    vc_trace.append_str( trace_str, "(" );

    // ''' LAB TASK ''''''''''''''''''''''''''''''''''''''''''''''''''''''
    // Add additional line tracing using the helper tasks for
    // internal state including the current FSM state.
    // '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''

    vc_trace.append_str( trace_str, ")" );

    $sformat( str, "%x", ostream_msg );
    vc_trace.append_val_rdy_str( trace_str, ostream_val, ostream_rdy, str );

  end
  `VC_TRACE_END

  `endif /* SYNTHESIS */

endmodule

`endif /* LAB1_IMUL_INT_MUL_BASE_V */

