{"ImportPassName": "VerilogVerilatorImportPass", "verilog_hash": "ad2e4b590b064111a91dc179ca0b8791fd6d287945f60e1f8232c78e59ceaba18f47e19b28dade8fe9ac85605d61565535a80db3d6c2284fad03e12ef519a46e", "vl_line_trace": true, "vl_coverage": false, "vl_line_coverage": false, "vl_toggle_coverage": false, "vl_mk_dir": "obj_dir_IntMulAlt_noparam", "vl_enable_assert": true, "vl_W_lint": true, "vl_W_style": true, "vl_W_fatal": true, "vl_Wno_list": ["UNOPTFLAT", "UNSIGNED", "WIDTH"], "vl_xinit": "zeros", "vl_trace": false, "vl_trace_timescale": "10ps", "vl_trace_cycle_time": 100, "vl_trace_on_demand": false, "vl_trace_on_demand_portname": "", "c_flags": "", "c_include_path": [], "c_srcs": [], "ld_flags": "", "ld_libs": "-l<PERSON><PERSON><PERSON>"}