// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Design implementation internals
// See VIntMulAlt_noparam.h for the primary calling header

#include "VIntMulAlt_noparam__pch.h"
#include "VIntMulAlt_noparam__Syms.h"
#include "VIntMulAlt_noparam___024root.h"

void VIntMulAlt_noparam___024root___ctor_var_reset(VIntMulAlt_noparam___024root* vlSelf);

VIntMulAlt_noparam___024root::VIntMulAlt_noparam___024root(VIntMulAlt_noparam__Syms* symsp, const char* v__name)
    : VerilatedModule{v__name}
    , vlSymsp{symsp}
 {
    // Reset structure values
    VIntMulAlt_noparam___024root___ctor_var_reset(this);
}

void VIntMulAlt_noparam___024root::__Vconfigure(bool first) {
    (void)first;  // Prevent unused variable warning
}

VIntMulAlt_noparam___024root::~VIntMulAlt_noparam___024root() {
}
