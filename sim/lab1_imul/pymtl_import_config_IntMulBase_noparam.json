{"ImportPassName": "VerilogVerilatorImportPass", "verilog_hash": "f221478eff77d276ab96861ca796598c20f6e8e346ce4ac3aabf6e1adfdaf1479ce5a362a566f0d92025b6098cfc5f754bdabc70e8f2dc37f7dcf63438341bd7", "vl_line_trace": true, "vl_coverage": false, "vl_line_coverage": false, "vl_toggle_coverage": false, "vl_mk_dir": "obj_dir_IntMulBase_noparam", "vl_enable_assert": true, "vl_W_lint": true, "vl_W_style": true, "vl_W_fatal": true, "vl_Wno_list": ["UNOPTFLAT", "UNSIGNED", "WIDTH"], "vl_xinit": "zeros", "vl_trace": false, "vl_trace_timescale": "10ps", "vl_trace_cycle_time": 100, "vl_trace_on_demand": false, "vl_trace_on_demand_portname": "", "c_flags": "", "c_include_path": [], "c_srcs": [], "ld_flags": "", "ld_libs": "-l<PERSON><PERSON><PERSON>"}