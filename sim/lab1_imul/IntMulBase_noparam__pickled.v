//-------------------------------------------------------------------------
// IntMulBase_noparam.v
//-------------------------------------------------------------------------
// This file is generated by PyMTL SystemVerilog translation pass.

// PyMTL VerilogPlaceholder IntMulBase Definition
// At /home/<USER>/ece4750/lab-group59/sim/lab1_imul/IntMulBase.py

//***********************************************************
// Pickled source file of placeholder IntMulBase_noparam
//***********************************************************

//-----------------------------------------------------------
// Dependency of placeholder IntMulBase
//-----------------------------------------------------------

`ifndef INTMULBASE
`define INTMULBASE

// The source code below are included because they are specified
// as the v_libs Verilog placeholder option of component IntMulBase_noparam.

// If you get a duplicated def error from files included below, please
// make sure they are included either through the v_libs option or the
// explicit `include statement in the Verilog source code -- if they
// appear in both then they will be included twice!


// End of all v_libs files for component IntMulBase_noparam

`line 1 "lab1_imul/IntMulBase.v" 0
//========================================================================
// Integer Multiplier Fixed-Latency Implementation
//========================================================================

`ifndef LAB1_IMUL_INT_MUL_BASE_V
`define LAB1_IMUL_INT_MUL_BASE_V

`line 1 "vc/trace.v" 0
//========================================================================
// Line Tracing
//========================================================================

`ifndef VC_TRACE_V
`define VC_TRACE_V

// NOTE: This macro is declared outside of the module to allow some vc
// modules to see it and use it in their own params. Verilog does not
// allow other modules to hierarchically reference the nbits localparam
// inside this module in constant expressions (e.g., localparams).

`define VC_TRACE_NCHARS 512
`define VC_TRACE_NBITS  512*8

module vc_Trace
(
  input logic clk,
  input logic reset
);

  integer len0;
  integer len1;
  integer idx0;
  integer idx1;

  // NOTE: If you change these, then you also need to change the
  // hard-coded constant in the declaration of the trace function at the
  // bottom of this file.
  // NOTE: You would also need to change the VC_TRACE_NBITS and
  // VC_TRACE_NCHARS macro at the top of this file.

  localparam nchars = 512;
  localparam nbits  = 512*8;

  // This is the actual trace storage used when displaying a trace

  logic [nbits-1:0] storage;

  // Meant to be accesible from outside module

  integer cycles_next = 0;
  integer cycles      = 0;

  // Get trace level from command line

  logic [3:0] level;

`ifndef VERILATOR
  initial begin
    if ( !$value$plusargs( "trace=%d", level ) ) begin
      level = 0;
    end
  end
`else
  initial begin
    level = 1;
  end
`endif // !`ifndef VERILATOR

  // Track cycle count

  always_ff @( posedge clk ) begin
    cycles <= ( reset ) ? 0 : cycles_next;
  end

  //----------------------------------------------------------------------
  // append_str
  //----------------------------------------------------------------------
  // Appends a string to the trace.

  task append_str
  (
    inout logic [nbits-1:0] trace,
    input logic [nbits-1:0] str
  );
  begin

    len0 = 1;
    while ( str[len0*8+:8] != 0 ) begin
      len0 = len0 + 1;
    end

    idx0 = trace[31:0];

    for ( idx1 = len0-1; idx1 >= 0; idx1 = idx1 - 1 )
    begin
      trace[ idx0*8 +: 8 ] = str[ idx1*8 +: 8 ];
      idx0 = idx0 - 1;
    end

    trace[31:0] = idx0;

  end
  endtask

  //----------------------------------------------------------------------
  // append_str_ljust
  //----------------------------------------------------------------------
  // Appends a left-justified string to the trace.

  task append_str_ljust
  (
    inout logic [nbits-1:0] trace,
    input logic [nbits-1:0] str
  );
  begin

    idx0 = trace[31:0];
    idx1 = nchars;

    while ( str[ idx1*8-1 -: 8 ] != 0 ) begin
      trace[ idx0*8 +: 8 ] = str[ idx1*8-1 -: 8 ];
      idx0 = idx0 - 1;
      idx1 = idx1 - 1;
    end

    trace[31:0] = idx0;

  end
  endtask

  //----------------------------------------------------------------------
  // append_chars
  //----------------------------------------------------------------------
  // Appends the given number of characters to the trace.

  task append_chars
  (
    inout logic   [nbits-1:0] trace,
    input logic         [7:0] char,
    input integer             num
  );
  begin

    idx0 = trace[31:0];

    for ( idx1 = 0;
          idx1 < num;
          idx1 = idx1 + 1 )
    begin
      trace[idx0*8+:8] = char;
      idx0 = idx0 - 1;
    end

    trace[31:0] = idx0;

  end
  endtask

  //----------------------------------------------------------------------
  // append_val_str
  //----------------------------------------------------------------------
  // Append a string modified by val signal.

  task append_val_str
  (
    inout logic [nbits-1:0] trace,
    input logic             val,
    input logic [nbits-1:0] str
  );
  begin

    len1 = 0;
    while ( str[len1*8+:8] != 0 ) begin
      len1 = len1 + 1;
    end

    if ( val )
      append_str( trace, str );
    else if ( !val )
      append_chars( trace, " ", len1 );
    else begin
      append_str( trace, "x" );
      append_chars( trace, " ", len1-1 );
    end

  end
  endtask

  //----------------------------------------------------------------------
  // val_rdy_str
  //----------------------------------------------------------------------
  // Append a string modified by val/rdy signals.

  task append_val_rdy_str
  (
    inout logic [nbits-1:0] trace,
    input logic             val,
    input logic             rdy,
    input logic [nbits-1:0] str
  );
  begin

    len1 = 0;
    while ( str[len1*8+:8] != 0 ) begin
      len1 = len1 + 1;
    end

    if ( rdy && val ) begin
      append_str( trace, str );
    end
    else if ( rdy && !val ) begin
      append_chars( trace, " ", len1 );
    end
    else if ( !rdy && val ) begin
      append_str( trace, "#" );
      append_chars( trace, " ", len1-1 );
    end
    else if ( !rdy && !val ) begin
      append_str( trace, "." );
      append_chars( trace, " ", len1-1 );
    end
    else begin
      append_str( trace, "x" );
      append_chars( trace, " ", len1-1 );
    end

  end
  endtask

endmodule

//------------------------------------------------------------------------
// VC_TRACE_NBITS_TO_NCHARS
//------------------------------------------------------------------------
// Macro to determine number of characters for a net

`define VC_TRACE_NBITS_TO_NCHARS( nbits_ ) ((nbits_+3)/4)

//------------------------------------------------------------------------
// VC_TRACE_BEGIN
//------------------------------------------------------------------------

//`define VC_TRACE_BEGIN                                                  \
//  export "DPI-C" task line_trace;                                       \
//  vc_Trace vc_trace(clk,reset);                                         \
//  task line_trace( inout bit [(512*8)-1:0] trace_str );

`ifndef VERILATOR
`define VC_TRACE_BEGIN                                                  \
  vc_Trace vc_trace(clk,reset);                                         \
                                                                        \
  task display_trace;                                                   \
  begin                                                                 \
                                                                        \
    if ( vc_trace.level > 0 ) begin                                     \
      vc_trace.storage[15:0] = vc_trace.nchars-1;                       \
                                                                        \
      line_trace( vc_trace.storage );                                   \
                                                                        \
      $write( "%4d: ", vc_trace.cycles );                               \
                                                                        \
      vc_trace.idx0 = vc_trace.storage[15:0];                           \
      for ( vc_trace.idx1 = vc_trace.nchars-1;                          \
            vc_trace.idx1 > vc_trace.idx0;                              \
            vc_trace.idx1 = vc_trace.idx1 - 1 )                         \
      begin                                                             \
        $write( "%s", vc_trace.storage[vc_trace.idx1*8+:8] );           \
      end                                                               \
      $write("\n");                                                     \
                                                                        \
    end                                                                 \
                                                                        \
    vc_trace.cycles_next = vc_trace.cycles + 1;                         \
                                                                        \
  end                                                                   \
  endtask                                                               \
                                                                        \
  task line_trace( inout bit [(512*8)-1:0] trace_str );
`else
`define VC_TRACE_BEGIN                                                  \
  export "DPI-C" task line_trace;                                       \
  vc_Trace vc_trace(clk,reset);                                         \
  task line_trace( inout bit [(512*8)-1:0] trace_str );
`endif

//------------------------------------------------------------------------
// VC_TRACE_END
//------------------------------------------------------------------------

`define VC_TRACE_END \
  endtask

`endif /* VC_TRACE_V */


`line 9 "lab1_imul/IntMulBase.v" 0

// ''' LAB TASK ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
// Define datapath and control unit here.
// '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''

// DATAPATH

module lab1_imul_datapath
(
  input logic clk,
  input logic reset,

  // Data signals

  input  logic [63:0] istream_msg,
  output logic [31:0] ostream_msg,

  // Control signals  

  input logic b_mux_sel,
  input logic a_mux_sel,
  input logic result_mux_sel,
  input logic result_en,
  input logic add_mux_sel,

  // Status signals

  output logic b_lsb
);

  localparam c_nbits = 32;

  // Split a and b operands
  logic [c_nbits-1:0] istream_msg_a;
  assign istream_msg_a = istream_msg[63:32];

  logic [c_nbits-1:0] istream_msg_b;
  assign istream_msg_b = istream_msg[31:0];

  // B Mux

  logic [c_nbits-1:0] rShift_out;
  logic [c_nbits-1:0] b_mux_out;

  vc_Mux2#(c_nbits) b_mux 
  (
    .sel (b_mux_sel),
    .in0 (rShift_out),
    .in1 (istream_msg_b),
    .out (b_mux_out)
  );

  // A Mux

  logic [c_nbits-1:0] lShift_out;
  logic [c_nbits-1:0] a_mux_out;

  vc_Mux2#(c_nbits) a_mux 
  (
    .sel (a_mux_sel),
    .in0 (lShift_out),
    .in1 (istream_msg_a),
    .out (a_mux_out)
  );

  // Result Mux

  logic [c_nbits-1:0] add_mux_out;
  logic [c_nbits-1:0] result_mux_out;

  vc_Mux2#(c_nbits) result_mux 
  (
    .sel (result_mux_sel),
    .in0 (add_mux_out),
    .in1 (1'b0),
    .out (result_mux_out)
  );

  // Add Mux

  logic [c_nbits-1:0] add_out;
  logic [c_nbits-1:0] result_reg_out;

  vc_Mux2#(c_nbits) result_mux 
  (
    .sel (add_mux_sel),
    .in0 (add_out),
    .in1 (result_reg_out),
    .out (add_mux_out)
  );

  // B register

  logic [c_nbits-1:0] b_reg_out;

  vc_ResetReg#(c_nbits) b_reg
  (
    .clk (clk),
    .reset (reset),
    .d (b_mux_out),
    .q (b_reg_out)
  );

  // A register

  logic [c_nbits-1:0] a_reg_out;

  vc_ResetReg#(c_nbits) a_reg
  (
    .clk (clk),
    .reset (reset),
    .d (a_mux_out),
    .q (a_reg_out)
  );

  // Result register

  vc_ResetReg#(c_nbits) result_reg
  (
    .clk (clk),
    .reset (reset),
    .d (result_mux_out),
    .q (result_reg_out)
  );

  // Adder

  vc_SimpleAdder#(c_nbits) adder
  (
    .in0 (a_reg_out),
    .in1 (result_reg_out),
    .out (add_out)
  );

  // Left shift

  logic [1:0] lShift_out;

  vc_LeftLogicalShifter#(.p_nbits(c_nbits), .p_shamt_nbits(1'b1)) lShifter
  (
    .in (a_reg_out),
    .shamt (1'b1),
    .out (lShift_out)
  );

  // Right shift

  logic [1:0] rShift_out;

  vc_RightLogicalShifter#(.p_nbits(c_nbits), .p_shamt_nbits(1'b1)) rShifter
  (
    .in (b_reg_out),
    .shamt (1'b1),
    .out (rShift_out)
  );

endmodule


// CONTROL UNIT

module lab1_imul_control
(
  input logic clk,
  input logic reset,

  // Data signals

  input  logic [63:0] istream_msg,
  output logic [31:0] ostream_msg,

  // Control signals  

  input logic b_mux_sel,
  input logic a_mux_sel,
  input logic result_mux_sel,
  input logic result_en,
  input logic add_mux_sel,

  // Status signals

  output logic b_lsb
);

  function void cs
  (
    input logic cs_istream_rdy,
    input logic cs_ostream_val,
    input logic b_mux_sel,
    input logic a_mux_sel,
    input logic result_mux_sel,
    input logic result_en,
    input logic add_mux_sel
  );
  begin
    istream_rdy = cs_istream_rdy;
    ostream_val = cs_ostream_val;
    b_mux_sel = cs_b_mux_sel;
    a_mux_sel = cs_a_mux_sel;
    result_mux_sel = cs_result_mux_sel;
    result_en = cs_result_en;
    add_mux_sel = cs_add_mux_sel;
  end
  endfunction

  logic do_add_shift;
  logic do_shift;
  logic [1:0] count_done;

  // Counter

  vc_BasicCounter#(.p_count_nbits(6), .p_count_max_value(32)) counter
  (
    .clk (clk),
    .reset (reset),
    .clear (1'b0),
    .increment(1'b1),
    .decrement(1'b0),
    .count_is_max(count_done)
  );

  assign do_add_shift = !count_done && b_lsb == 1;
  assign do_shift = !count_done && b_lsb == 0;

  logic [2:0] current_state;
  localparam state_idle = 2'b00;
  localparam state_calc = 2'b01;
  localparam state_done = 2'b10;
endmodule

// Sequential block for state element

always_ff begin
  if (reset) current_state <= state_idle;
  else current_state <= next_state;
end

// Combinational block for state transitions

always_comb begin
  case (current_state)
    state_idle: begin
      if (istream_val) next_state = state_calc;
      else next_state = state_idle;
    end

    state_calc: begin
      if (count_done) next_state = state_done;
      else next_state = state_calc;
    end

    state_done: begin
      if (ostream_rdy) current_state = state_idle;
      else next_state = state_done;
    end

    default: state = state_idle;
  endcase
end

// Combinational block for state outputs

  input logic cs_istream_rdy,
  input logic cs_ostream_val,
  input logic b_mux_sel,
  input logic a_mux_sel,
  input logic result_mux_sel,
  input logic result_en,
  input logic add_mux_sel

always_comb begin
  case (current_state)
    state_idle: cs(1, 0, 0, 0, 1, 0, 0);
    state_calc: if (do_add_shift) cs(0, 0, 0, 0, 0, 1, 0);
    else if (do_shift) cs(0, 0, 0, 0, 0, 1, 1);
    state_done: cs(0, 1, 'x, 'x, 0, 1, 1);
    default: cs(x, x, x, x, x, x, x);
  endcase
end

//========================================================================
// Integer Multiplier Fixed-Latency Implementation
//========================================================================

module lab1_imul_IntMulBase
(
  input  logic        clk,
  input  logic        reset,

  input  logic        istream_val,
  output logic        istream_rdy,
  input  logic [63:0] istream_msg,

  output logic        ostream_val,
  input  logic        ostream_rdy,
  output logic [31:0] ostream_msg
);

  // ''' LAB TASK ''''''''''''''''''''''''''''''''''''''''''''''''''''''''
  // Instantiate datapath and control models here and then connect them
  // together.
  // '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''

  lab1_imul_datapath dpath
  (
    .*
  );

  lab1_imul_datapath ctrl
  (
    .*
  );


  //----------------------------------------------------------------------
  // Line Tracing
  //----------------------------------------------------------------------

  `ifndef SYNTHESIS

  logic [`VC_TRACE_NBITS-1:0] str;
  `VC_TRACE_BEGIN
  begin

    $sformat( str, "%x", istream_msg );
    vc_trace.append_val_rdy_str( trace_str, istream_val, istream_rdy, str );

    vc_trace.append_str( trace_str, "(" );

    // ''' LAB TASK ''''''''''''''''''''''''''''''''''''''''''''''''''''''
    // Add additional line tracing using the helper tasks for
    // internal state including the current FSM state.
    // '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''

    vc_trace.append_str( trace_str, ")" );

    $sformat( str, "%x", ostream_msg );
    vc_trace.append_val_rdy_str( trace_str, ostream_val, ostream_rdy, str );

  end
  `VC_TRACE_END

  `endif /* SYNTHESIS */

endmodule

`endif /* LAB1_IMUL_INT_MUL_BASE_V */


`endif /* INTMULBASE */
//-----------------------------------------------------------
// Wrapper of placeholder IntMulBase_noparam
//-----------------------------------------------------------

`ifndef INTMULBASE_NOPARAM
`define INTMULBASE_NOPARAM

module IntMulBase_noparam
(
  input logic [1-1:0] clk ,
  input logic [1-1:0] reset ,
  input logic [64-1:0] istream_msg ,
  output logic [1-1:0] istream_rdy ,
  input logic [1-1:0] istream_val ,
  output logic [32-1:0] ostream_msg ,
  input logic [1-1:0] ostream_rdy ,
  output logic [1-1:0] ostream_val 
);
  lab1_imul_IntMulBase
  #(
  ) v
  (
    .clk( clk ),
    .reset( reset ),
    .istream_msg( istream_msg ),
    .istream_rdy( istream_rdy ),
    .istream_val( istream_val ),
    .ostream_msg( ostream_msg ),
    .ostream_rdy( ostream_rdy ),
    .ostream_val( ostream_val )
  );
endmodule

`endif /* INTMULBASE_NOPARAM */

